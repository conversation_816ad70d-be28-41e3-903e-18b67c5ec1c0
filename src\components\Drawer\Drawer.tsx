import React from "react"
import { createPortal } from "react-dom"

import { Backdr<PERSON>, <PERSON>er<PERSON>ooter, Header } from "./components"
import { useDrawer } from "./hooks"

import { StyledDrawer } from "./StyledDrawer"

import { DrawerProps } from "./DrawerTypes"

export const Drawer = ({
  isOpen,
  children,
  title,
  subTitle,
  side = "right",
  width = "xs",
  headerHeight,
  height = "100%",
  top = 0,
  bottom = 0,
  hasPadding = false,
  hasBackdrop = true,
  hasBorderRadius = false,
  hasHeaderBottomMargin = false,
  onClose,
  onBack,
  onOk,
  zIndex = 1001,
  hasAnimation = true,
  hasFooter = false,
  isStickyFooter = false,
  footer,
  footerClassName,
  cancelButtonText,
  cancelButtonProps,
  okButtonText,
  okButtonProps,
  hasHeader = true,
  isColumnButtonsOnMobile = false,
  isLoading,
  isKeyboard,
  isClosable = true,
  isClosedAfterOk,
}: DrawerProps) => {
  const { shouldR<PERSON>Header, shouldRenderColumnButtonsOnMobile, handleOk } =
    useDrawer({
      onBack,
      onClose,
      onOk,
      isOpen,
      isClosable,
      isClosedAfterOk,
      isKeyboard,
      isColumnButtonsOnMobile,
      hasHeader,
      title,
      subTitle,
    })

  return createPortal(
    <>
      {hasBackdrop ? (
        <Backdrop isOpen={isOpen} zIndex={zIndex} onClose={onClose} />
      ) : null}
      <StyledDrawer
        bottom={bottom}
        hasAnimation={hasAnimation}
        hasBorderRadius={hasBorderRadius}
        hasPadding={hasPadding}
        height={height}
        isOpen={isOpen}
        side={side}
        top={top}
        width={width}
        zIndex={zIndex}
      >
        <div>
          {shouldRenderHeader ? (
            <Header
              hasHeaderBottomMargin={hasHeaderBottomMargin}
              headerHeight={headerHeight}
              subTitle={subTitle}
              title={title}
              onBack={onBack}
              onClose={onClose}
            />
          ) : null}
          <div data-component-type="contents">{children}</div>
          {hasFooter ? (
            <DrawerFooter
              cancelButtonProps={cancelButtonProps}
              cancelButtonText={cancelButtonText}
              footer={footer}
              footerClassName={footerClassName}
              isColumnButtonsOnMobile={shouldRenderColumnButtonsOnMobile}
              isLoading={isLoading}
              isStickyFooter={isStickyFooter}
              okButtonProps={okButtonProps}
              okButtonText={okButtonText}
              onClose={onClose}
              onOk={handleOk}
            />
          ) : null}
        </div>
      </StyledDrawer>
    </>,
    document.body
  )
}
