const path = require("path")
const { merge } = require("webpack-merge")
const HtmlWebpackPlugin = require("html-webpack-plugin")

const baseWebpackConfig = require("./webpack.config.base")

module.exports = merge(baseWebpackConfig, {
  entry: [
    path.resolve(__dirname, "./src/App.tsx"),
    path.resolve(__dirname, "./src/index.tsx"),
  ],
  mode: "development",
  module: {
    rules: [
      {
        test: /\.ico$/i,
        type: "asset/resource",
        generator: {
          filename: "[name][ext][query]",
        },
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      title: "SellerLogic Ui Kit",
      template: "./public/index.html",
      favicon: "./public/favicon.ico",
      meta: {
        viewport: "width=device-width, initial-scale=1, maximum-scale=1",
      },
    }),
    // HotModuleReplacementPlugin is no longer needed in webpack 5 when using devServer
  ],
  output: {
    filename: "[name].js",
    path: path.resolve(__dirname, "dist"),
    clean: true,
    publicPath: "/",
  },
  optimization: {
    // Disable code splitting to avoid chunk loading issues
    splitChunks: false,
    runtimeChunk: false,
  },
  devServer: {
    static: path.resolve(__dirname, "dist"),
    open: true,
    historyApiFallback: true,
    hot: true, // Enable HMR in devServer
  },
})
