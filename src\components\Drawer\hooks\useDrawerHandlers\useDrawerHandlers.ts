import { useEffect } from "react"

import { UseDrawerHandlers } from "../../DrawerTypes"

export const useDrawerHandlers: UseDrawerHandlers = ({
  onClose,
  onOk,
  isClosable,
  isKeyboard,
  isClosedAfterOk,
  isOpen,
}) => {
  const isClosableByKeyPress: boolean = isKeyboard && isOpen && isClosable

  useEffect(() => {
    const onKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose?.()
      }
    }

    if (isClosableByKeyPress) {
      window.addEventListener("keydown", onKeyPress)
    }

    return () => {
      window.removeEventListener("keydown", onKeyPress)
    }
  }, [isOpen, isClosableByKeyPress])

  const handleOk = () => {
    onOk?.()

    if (isClosedAfterOk) {
      onClose?.()
    }
  }

  return {
    handleOk,
  }
}
