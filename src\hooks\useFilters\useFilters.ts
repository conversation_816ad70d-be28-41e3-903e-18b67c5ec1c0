import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import debounce from "lodash.debounce"
import isEqual from "lodash.isequal"
import type { DeepPartial, UseFormReturn } from "react-hook-form"
import { useForm } from "react-hook-form"

import { useUpdateIfChanged } from "hooks"
import {
  type NormalizeFilterValuesToUrlParams,
  type ParseFilterValuesFromUrlParams,
  checkIsFunction,
  normalizeFilterValuesToUrl as normalizeFilterValuesToUrlDefault,
  parseFilterValuesFromUrl as parseFilterValuesFromUrlDefault,
  removeEmptyArrays,
  removeNullAndUndefined,
} from "utils"
import { FilterPaginationValues, FilterSortValues } from "types"

import {
  getUrlParams,
  getUrlSearch,
  updateFilterStates,
  updateUrlSearchDefault,
} from "./utils"
import { DEBOUNCE_TIME_DEFAULT, RESET_OPTIONS } from "./constants"

import {
  FilterStates,
  UseFiltersParams,
  UseFiltersReturn,
} from "./UseFiltersTypes"

export const useFilters = <
  FilterValuesType extends FilterSortValues & FilterPaginationValues
>({
  values: valuesProp,
  defaultValues,
  injectedValues,
  urlSearchDefaultValue,
  onSearch,
  prefix = "",
  debounceTime = DEBOUNCE_TIME_DEFAULT,
  isConnectedToUrl = true,
  updateUrlSearch: updateUrlSearchParam,
  parseFilterValuesFromUrl: parseFilterValuesFromUrlParam,
  normalizeFilterValuesToUrl: normalizeFilterValuesToUrlParam,
  targetKeys,
  numberKeys,
  dateKeys,
  dateTimeKeys,
  dateRangeKeys,
  selectWithSearchKeys,
  hiddenKeys,
  persistOnClearKeys,
  shouldPersistInjectedValuesOnClear,
  resetFiltersWatchList,
}: UseFiltersParams<FilterValuesType>): UseFiltersReturn<FilterValuesType> => {
  const updateUrlSearch = useCallback(
    (urlSearch: string): void => {
      if (checkIsFunction(updateUrlSearchParam)) {
        updateUrlSearchParam(urlSearch)
      }

      updateUrlSearchDefault(urlSearch)
    },
    [updateUrlSearchParam]
  )

  const parseFilterValuesFromUrl = useCallback(
    (params: ParseFilterValuesFromUrlParams): DeepPartial<FilterValuesType> => {
      if (checkIsFunction(parseFilterValuesFromUrlParam)) {
        return parseFilterValuesFromUrlParam(params)
      }

      return parseFilterValuesFromUrlDefault(params)
    },
    [parseFilterValuesFromUrlParam]
  )

  const normalizeFilterValuesToUrl = useCallback(
    (
      params: NormalizeFilterValuesToUrlParams<FilterValuesType>
    ): Record<string, string> => {
      if (checkIsFunction(normalizeFilterValuesToUrlParam)) {
        return normalizeFilterValuesToUrlParam(params)
      }

      return normalizeFilterValuesToUrlDefault(params)
    },
    [normalizeFilterValuesToUrlParam]
  )

  const defaultValuesProcessed = useMemo(() => {
    const { targetParams, restParams } = getUrlParams<FilterValuesType>({
      targetKeys,
      prefix,
      urlSearch: urlSearchDefaultValue,
    })

    const parsedValues = parseFilterValuesFromUrl({
      values: targetParams,
      restValues: restParams,
      numberKeys,
      dateKeys,
      dateTimeKeys,
      dateRangeKeys,
      selectWithSearchKeys,
    })

    return {
      ...defaultValues,
      ...parsedValues,
      ...injectedValues,
    }
  }, [])

  const form = useForm<FilterValuesType>({
    defaultValues: defaultValuesProcessed,
  })

  const { watch, reset, getValues } = form

  const [filterStates, setFilterStates] = useState<FilterStates>(() => {
    const defaultValuesFiltered = removeEmptyArrays(
      removeNullAndUndefined(defaultValuesProcessed)
    )

    return {
      hasActiveFilters: false,
      hasActiveHiddenFilters: false,
      activeFilters: new Set(Object.keys(defaultValuesFiltered)),
    }
  })

  const urlSearchRef = useRef<string>()
  const [initialPath] = useState(window.location.pathname)

  const allValues = watch()
  const allValuesRef = useRef<FilterValuesType>()

  useEffect(() => {
    if (isEqual(allValues, allValuesRef.current)) {
      return
    }

    const handleSearch = debounce(() => {
      // Remove null and undefined values from search params
      const params = removeEmptyArrays(removeNullAndUndefined(allValues || {}))

      // Check if there are active filter, active hidden filters and generate a set of active filters for table content
      updateFilterStates({
        params,
        hiddenKeys,
        persistOnClearKeys,
        setFilterStates,
        resetFiltersWatchList,
      })

      // Generate search string taking into account normalization function
      const urlSearchNew: string = getUrlSearch({
        values: params,
        prefix,
        normalizeFilterValuesToUrl,
        targetKeys,
        dateKeys,
        dateTimeKeys,
        dateRangeKeys,
        selectWithSearchKeys,
      })

      // Update URL search if it is connected to URL and URL search has changed
      const shouldUpdateUrlSearch: boolean =
        isConnectedToUrl &&
        urlSearchNew !== document.location.search &&
        checkIsFunction(updateUrlSearch) &&
        urlSearchRef.current !== urlSearchNew

      urlSearchRef.current = urlSearchNew

      if (shouldUpdateUrlSearch) {
        updateUrlSearch(urlSearchNew)
      }

      // Call onSearch function if it is provided
      if (checkIsFunction(onSearch)) {
        onSearch({
          params,
          searchString: urlSearchNew,
        })
      }

      allValuesRef.current = allValues
    }, debounceTime)

    handleSearch()

    return () => {
      handleSearch.cancel()
    }
  }, [
    allValues,
    hiddenKeys,
    persistOnClearKeys,
    prefix,
    normalizeFilterValuesToUrl,
    targetKeys,
    dateKeys,
    dateTimeKeys,
    dateRangeKeys,
    selectWithSearchKeys,
    isConnectedToUrl,
    updateUrlSearch,
    onSearch,
  ])

  // Update form values when controlled state is changed. When form values change, it will trigger search
  useUpdateIfChanged<FilterValuesType>({
    values: valuesProp,
    updateValues: (valuesNew) => {
      reset(valuesNew, RESET_OPTIONS)
    },
  })

  // Update form values when injected state is changed. When form values change, it will trigger search
  useUpdateIfChanged<Partial<FilterValuesType>>({
    values: injectedValues,
    updateValues: (injectedValuesNew) => {
      const valuesNew = {
        ...getValues(),
        ...injectedValuesNew,
      }

      reset(valuesNew, RESET_OPTIONS)
    },
  })

  useEffect(() => {
    if (!isConnectedToUrl) {
      return
    }

    const handleUrlSearchChange = (): void => {
      if (initialPath !== window.location.pathname) {
        return
      }

      if (document.location.search === urlSearchRef.current) {
        return
      }

      const { targetParams, restParams } = getUrlParams<FilterValuesType>({
        targetKeys,
        prefix,
      })

      const parsedParams = parseFilterValuesFromUrl({
        values: targetParams,
        restValues: restParams,
        numberKeys,
        dateKeys,
        dateTimeKeys,
        dateRangeKeys,
        selectWithSearchKeys,
      })

      const valuesOld = getValues()

      const valuesNew = {
        ...defaultValues,
        ...parsedParams,
        ...injectedValues,
      }

      const valuesOldProcessed = removeEmptyArrays(
        removeNullAndUndefined(valuesOld)
      )
      const valuesNewProcessed = removeEmptyArrays(
        removeNullAndUndefined(valuesNew)
      )

      if (isEqual(valuesNewProcessed, valuesOldProcessed)) {
        return
      }

      reset(valuesNew, RESET_OPTIONS)
    }

    handleUrlSearchChange()

    window.addEventListener("locationchange", handleUrlSearchChange)

    return () => {
      window.removeEventListener("locationchange", handleUrlSearchChange)
    }
  }, [
    isConnectedToUrl,
    defaultValues,
    injectedValues,
    prefix,
    parseFilterValuesFromUrl,
  ])

  const handleClear = useCallback((): void => {
    const values = getValues()

    const valuesNew: DeepPartial<FilterValuesType> = persistOnClearKeys.reduce(
      (acc, key) => {
        if (!values[key]) {
          return acc
        }

        return {
          ...acc,
          [key]: values[key],
        }
      },
      shouldPersistInjectedValuesOnClear
        ? {
            ...defaultValuesProcessed,
            ...injectedValues,
          }
        : defaultValuesProcessed
    )

    reset(valuesNew, RESET_OPTIONS)
  }, [
    defaultValues,
    injectedValues,
    persistOnClearKeys,
    defaultValuesProcessed,
    shouldPersistInjectedValuesOnClear,
  ])

  return {
    form: form as UseFormReturn<FilterValuesType>,
    handleClear,
    ...filterStates,
  }
}
