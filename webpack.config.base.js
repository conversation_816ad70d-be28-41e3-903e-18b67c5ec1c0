const path = require("path")

module.exports = {
  target: "web",
  module: {
    rules: [
      // JSON imports are natively supported in webpack 5, json-loader is no longer needed
      {
        test: /\.(sass|css|scss)$/,
        use: ["style-loader", "css-loader", "postcss-loader", "sass-loader"],
      },
      {
        test: /\.(ts|tsx)/,
        use: [
          {
            loader: "babel-loader",
          },
          {
            loader: "ts-loader",
            options: {
              // Enable transpileOnly as types will be generated separately
              transpileOnly: true,
            },
          },
        ],
        exclude: /node_modules/,
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: '@svgr/webpack',
            options: {
              svgoConfig: {
                plugins: [
                  {
                    name: 'preset-default',
                    params: {
                      overrides: {
                        removeViewBox: false
                      }
                    }
                  }
                ]
              },
              exportType: 'named',
              typescript: true,
              ext: 'tsx'
            }
          },
          {
            loader: 'file-loader',
            options: {
              name: 'static/icons/[name].[hash].[ext]'
            }
          }
        ]
      },
      {
        test: /\.(png|jpg|gif|webp)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        type: "asset/resource",
        generator: {
          filename: "static/images/[name][ext][query]",
        },
      },
      {
        test: /\.(tsx|ts)$/,
        loader: "stylelint-custom-processor-loader",
        exclude: /node_modules/,
        // Only process files that likely contain styled-components
        include: path.resolve(__dirname, "src"),
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".jsx", ".scss"],
    alias: {
      utils: path.resolve(__dirname, "./src/utils/"),
      hooks: path.resolve(__dirname, "./src/hooks/"),
      hocs: path.resolve(__dirname, "./src/hocs/"),
      components: path.resolve(__dirname, "./src/components/"),
      assets: path.resolve(__dirname, "./src/assets/"),
      consts: path.resolve(__dirname, "./src/consts/"),
      globalStyles: path.resolve(__dirname, "./src/globalStyles/"),
      routes: path.resolve(__dirname, "./src/routes/"),
    },
  },
  plugins: [
    // Plugins will be added in specific configurations (dev/prod)
  ],
}

