/**
 *
 * Namespace with types and utilities for SellerLogic.
 * Use it to work with object types and other helper types.
 *
 * @namespace SLTypes
 *
 * @property {ObjectValues} SLTypes.ObjectValues - Returns a union of all values of an object.
 */
declare global {
  namespace SLTypes {
    export type ObjectValues<T> = T extends Record<string, unknown>
      ? T[keyof T]
      : never
  }
}

export {}
