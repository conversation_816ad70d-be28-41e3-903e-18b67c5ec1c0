import React, { useState } from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import {
  Button,
  Checkbox,
  Collapse as CollapseComponent,
  CollapseProps,
  Flag,
  Icon,
  TextInput,
  Typography,
} from "components"
import { Item, ItemKey } from "components/Collapse/CollapseTypes"

const defaultItems: Item[] = [...Array(10).keys()].map((key) => ({
  key,
  header: `Header ${key}`,
  contents: `Content ${key}`,
}))

export default {
  title: "Collapse",
  component: CollapseComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [Collapse](https://app.zeplin.io/project/6051cc6c5146ec6cbe778ec2/screen/60ba2629c7003796fb68e143)",
      },
    },
  },
  argTypes: {
    items: {
      description:
        "Array of objects each having key, header, contents, disabled, and disabledPopoverMessage. Key is a unique identifier: number or string. Header and contents can be ReactNode or a function with specific parameters returning ReactNode. Disabled is a state of the item and disabledPopoverMessage is a popover message appearing when the disabled item is hovered.",
      defaultValue: defaultItems,
    },
    activeKeys: {
      description:
        "Array of active items. Used to control the states from outside the component.",
      defaultValue: undefined,
    },
    defaultActiveKeys: {
      description: "Array of initially active items.",
      defaultValue: [],
    },
    variant: {
      description:
        "Collapse styles depend on variant. Default has white background and no border between header and contents. Outlined has grey background and border between header and contents.",
      defaultValue: "default",
    },
    headerPadding: {
      description: "Affects the padding of the header.",
    },
    headerGap: {
      description: "Affects the gap between header contents.",
    },
    isContentAlignedWithArrowBox: {
      description:
        "Dictates if the content left side should be aligned with the edge of chevron icon box. If there is no icon box, this prop has no effect.",
      defaultValue: true,
    },
    isAccordion: {
      description: "Dictates if only one panel can be expanded at once.",
      defaultValue: false,
    },
    hasChevron: {
      description: "Dictates if the headers have default chevron icon.",
      defaultValue: true,
    },
    hasLeftAndRightBorders: {
      description: "Includes left and right borders to the collapse container.",
      defaultValue: false,
    },
    hasTopAndBottomBorders: {
      description: "Includes top and bottom borders to the collapse container.",
      defaultValue: true,
    },
    onChange: {
      description: "Dispatches on opening/closing any panel.",
      defaultValue: undefined,
    },
  },
} as ComponentMeta<typeof CollapseComponent>

const Template: ComponentStory<typeof CollapseComponent> = ({
  ...props
}: CollapseProps) => {
  return <CollapseComponent items={defaultItems} {...props} />
}

export const Collapse = Template.bind({})

export const CollapseConfigurations = () => {
  const items = defaultItems.slice(0, 3)

  return (
    <div>
      <p>
        Each item consists of key, header, and contents. Key can be either
        string or number and should has a unique value. Header and contents can
        be a ReactNode.
      </p>
      <p>
        Default variant has white background and no border between header and
        its expanded contents.
      </p>
      <CollapseComponent items={items} />
      <br />
      <p>
        Outlined variant has grey background and a border between header and its
        expanded contents.
      </p>
      <CollapseComponent items={items} variant="outlined" />
      <br />
      <p>
        Outer borders can be set using props <b>hasLeftAndRightBorders</b>{" "}
        (false by default) and <b>hasTopAndBottomBorders</b> (true by default)
      </p>
      <br />
      <p>Default:</p>
      <CollapseComponent hasLeftAndRightBorders={false} items={items} />
      <br />
      <p>All borders:</p>
      <CollapseComponent hasLeftAndRightBorders items={items} />
      <br />
      <p>No borders:</p>
      <CollapseComponent hasTopAndBottomBorders={false} items={items} />
    </div>
  )
}

export const Accordion = () => {
  const items = defaultItems.slice(0, 3)

  return (
    <div>
      <p>
        If prop <b>isAccordion=true</b>, only one panel can be expanded at once.
      </p>
      <br />
      <CollapseComponent isAccordion items={items} />
    </div>
  )
}

export const DisabledPanels = () => {
  const items = defaultItems.map((item) => ({
    ...item,
    disabled: item.key > 4,
    disabledPopoverMessage: item.key > 4 ? "Disabled" : null,
  }))

  return (
    <div>
      <p>
        Disabled items cannot be clicked and have a popover with a user defined
        message. In the example below, all panels after 4 are disabled.
      </p>
      <br />
      <CollapseComponent isAccordion items={items} />
    </div>
  )
}

export const ControlledState = () => {
  const [activeKeys, setActiveKeys] = useState<Array<ItemKey>>([0, 1, 2, 3])

  return (
    <div>
      <p>
        Collapse states can be controlled from outside of the component via
        activeKeys prop.
      </p>
      <p>
        If the item is disabled, then its panel cannot be expanded by any means.
        If the activeKeys prop includes a disabled key, it is ignored. (First
        two items disabled in the example below.)
      </p>
      <br />
      <div
        style={{
          display: "grid",
          gap: 10,
          gridTemplateColumns: "repeat(10, 80px)",
        }}
      >
        {defaultItems.map((item) => {
          const isExpanded = activeKeys.includes(item.key)

          return (
            <Button
              onClick={() => {
                let activeKeysNewState
                if (activeKeys.includes(item.key)) {
                  activeKeysNewState = activeKeys.filter(
                    (key) => key !== item.key
                  )
                } else {
                  activeKeysNewState = [...activeKeys, item.key]
                }
                setActiveKeys(activeKeysNewState)
              }}
            >
              {isExpanded ? "Close" : "Open"} {item.key}
            </Button>
          )
        })}
      </div>
      <br />
      <br />
      <CollapseComponent
        activeKeys={activeKeys}
        items={defaultItems.map((item) => ({
          ...item,
          disabled: item.key < 2,
        }))}
        onChange={(params) => {
          setActiveKeys(params.activeKeys)
        }}
      />
    </div>
  )
}

export const CustomHeaders = () => {
  const itemsWithCustomHeaders = defaultItems.map((item) => ({
    ...item,
    // eslint-disable-next-line react/no-unstable-nested-components
    header: ({ isExpanded, toggle }) => {
      const chevronIcnName = isExpanded ? "icnArrowUp" : "icnArrowDown"

      return (
        <div
          style={{
            display: "flex",
            gap: 20,
            alignItems: "center",
            height: "100%",
            width: "100%",
            backgroundColor: "beige",
          }}
          onClick={(event) => {
            event.stopPropagation()
          }}
        >
          <div
            style={{
              paddingRight: 20,
              paddingLeft: 20,
              borderRight: "1px solid #d5dce0",
              cursor: "pointer",
              background: "crimson",
              height: "100%",
              display: "flex",
              alignItems: "center",
            }}
            onClick={toggle}
          >
            <Icon
              color="--color-icon-white"
              name={chevronIcnName}
              size="--icon-size-4"
            />
          </div>
          <Icon name="icnAppStore" />
          Some custom colored header {item.key}
          <TextInput
            label={`Some input ${item.key}`}
            onClick={(event) => {
              event.stopPropagation()
            }}
          />
          <div
            style={{
              flexGrow: 1,
              backgroundColor: "lightcyan",
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            Different color
          </div>
        </div>
      )
    },
  }))

  return (
    <CollapseComponent
      hasChevron={false}
      headerPadding="0"
      items={itemsWithCustomHeaders}
    />
  )
}

export const AdminRolesCollapse = () => {
  const renderCheckboxItem = (text) => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
        }}
      >
        <Checkbox checked />
        {text}
      </div>
    )
  }

  const renderRow = (columns) => {
    return columns.map((column) => {
      return <div>{renderCheckboxItem(column)}</div>
    })
  }

  const restRoles = [
    {
      name: "Product group",
      items: [
        "View",
        "Create/Update (Readonly)",
        "Create/Update (Manage)",
        "Delete",
        "ViewColumn",
      ],
    },
    {
      name: "Strategy templates",
      items: [
        "View",
        "Create/Update (Readonly)",
        "Create/Update (Manage)",
        "Delete",
      ],
    },
    {
      name: "Scheduler stops",
      items: [
        "View",
        "Create/Update (Readonly)",
        "Create/Update (Manage)",
        "Delete",
      ],
    },
    { name: "Order statistic", items: ["View"] },
    { name: "Import", items: ["View", "Upload File"] },
  ]

  const items = [
    {
      key: "common",
      header: "Common permissions",
      contents: (
        <>
          <div
            style={{
              overflow: "auto",
              display: "grid",
              gridTemplateColumns: "155px 180px 180px 180px 180px 180px 180px",
              padding: "20px 20px 20px 0",
              gap: 20,
              alignItems: "start",
            }}
          >
            <div>Amazon product</div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: 10,
              }}
            >
              {renderCheckboxItem("View")}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  marginLeft: 20,
                  gap: 10,
                }}
              >
                {[...Array(6).keys()].map((key) => {
                  return renderCheckboxItem(`View parent ${key + 1}`)
                })}
              </div>
            </div>
            {renderCheckboxItem("Settings (Readonly)")}
            {renderCheckboxItem("Settings(Manage)")}
            {renderCheckboxItem("Group Action (Readonly)")}
            {renderCheckboxItem("Group Action (Manage)")}
            {renderCheckboxItem(" Service Access")}
          </div>
          {restRoles.map((role) => {
            return (
              <div
                style={{
                  overflow: "auto",
                  display: "grid",
                  gridTemplateColumns:
                    "155px 180px 180px 180px 180px 180px 180px",
                  padding: "20px 20px 20px 0",
                  gap: 20,
                  alignItems: "start",
                }}
              >
                <div>{role.name}</div>
                {renderRow(role.items)}
              </div>
            )
          })}
        </>
      ),
    },
    {
      key: "rp",
      header: "Repricer",
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
    {
      key: "lf",
      header: "Lost&Found",
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
    {
      key: "sd",
      header: "Service Desk",
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
    {
      key: "ba",
      header: "Business Analytics",
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
  ]

  return (
    <div>
      <p>
        <a
          href="https://app.zeplin.io/project/6051cc6c5146ec6cbe778ec2/screen/60ba2629c7003796fb68e143"
          target="_blank"
        >
          Design
        </a>
      </p>
      <br />
      <CollapseComponent
        defaultActiveKeys={["common"]}
        items={items.map((item) => ({
          ...item,
          header: (
            <Typography variant="--font-headline-5">{item.header}</Typography>
          ),
        }))}
      />
    </div>
  )
}

export const BasEditGroupsCollapse = () => {
  const renderHeader = (key) => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "450px 250px auto",
          alignItems: "center",
          width: "100%",
          paddingRight: 20,
        }}
      >
        <div
          style={{
            borderRight: "1px solid #d5dce0",
            height: 32,
            display: "flex",
            alignItems: "center",
            borderLeft: "1px solid #d5dce0",
            marginLeft: "-10px",
            paddingLeft: "10px",
          }}
        >
          <Typography variant="--font-body-text-2">Group name {key}</Typography>
        </div>
        <div
          style={{
            borderRight: "1px solid #d5dce0",
            marginLeft: 10,
            height: 32,
            display: "flex",
            alignItems: "center",
            gap: 10,
          }}
        >
          Default currency:{" "}
          <Typography color="--color-text-second" variant="--font-body-text-9">
            USD
          </Typography>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "end",
            gap: 10,
            marginLeft: 10,
          }}
        >
          <Button iconOnly icon="icnEdit" variant="secondary" />
          <Button iconOnly icon="icnDeleteOutlined" variant="secondary" />
        </div>
      </div>
    )
  }
  const renderRow = (locales) => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "450px 250px auto",
          alignItems: "center",
          borderBottom: "1px solid #d5dce0",
          paddingLeft: 57,
          paddingRight: 20,
          paddingTop: 10,
          paddingBottom: 10,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: 5,
            borderRight: "1px solid #d5dce0",
            height: 32,
          }}
        >
          Marketplaces:{" "}
          {locales.map((locale) => (
            <Flag
              borderRadius="--border-radius-circle"
              locale={locale}
              size={24}
            />
          ))}
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: 5,
            borderRight: "1px solid #d5dce0",
            height: 32,
            paddingLeft: 10,
          }}
        >
          Home marketplace:{" "}
          <Flag borderRadius="--border-radius-circle" locale="de" size={24} />
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: 5,
            height: 32,
            paddingLeft: 10,
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          Account:{" "}
          <Typography color="--color-text-second" variant="--font-body-text-9">
            Account-2343
          </Typography>
        </div>
      </div>
    )
  }

  const items = [
    {
      key: 0,
      header: renderHeader(0),
      contents: (
        <>
          {renderRow([
            "fr",
            "nl",
            "es",
            "gb",
            "it",
            "ae",
            "in",
            "tr",
            "sa",
            "eg",
            "de",
          ])}
          {renderRow(["fr", "nl", "de"])}
          {renderRow(["fr", "nl", "es", "gb", "it", "ae", "de"])}
          {renderRow(["fr", "nl", "es", "de"])}
        </>
      ),
    },
    {
      key: 1,
      header: renderHeader(1),
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
    {
      key: 2,
      header: renderHeader(2),
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
    {
      key: 3,
      header: renderHeader(3),
      contents: "",
      disabled: true,
      disabledPopoverMessage: "Disabled",
    },
  ]

  return (
    <div>
      <p>
        <a
          href="https://app.zeplin.io/project/6194ab8d809153a9a48658fb/screen/6194f31f28dd3fad9d60b07e"
          target="_blank"
        >
          Design
        </a>
      </p>
      <br />
      <CollapseComponent
        defaultActiveKeys={[0]}
        headerGap="l"
        headerPadding="m l"
        isContentAlignedWithArrowBox={false}
        isEventPropagationStopped={false}
        items={items}
        variant="outlined"
      />
    </div>
  )
}
