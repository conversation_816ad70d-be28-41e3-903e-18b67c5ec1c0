{"compilerOptions": {"lib": ["ES6", "ES7", "dom"], "sourceMap": true, "declaration": true, "declarationDir": "dist/types", "emitDeclarationOnly": true, "esModuleInterop": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "noUnusedLocals": false, "noUnusedParameters": false, "resolveJsonModule": true, "jsx": "react", "target": "ESNext", "module": "ESNext", "baseUrl": "src", "outDir": "dist", "plugins": [{"name": "typescript-plugin-css-modules"}], "skipLibCheck": true}, "include": ["src/**/*", "./declarations.d.ts", "./global.ts"], "exclude": ["node_modules", "dist/**/*", "App.tsx"]}