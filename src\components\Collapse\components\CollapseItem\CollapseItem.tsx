import React, { useMemo } from "react"

import { Icon, Popover } from "components"
import { PLACEMENTS_ORDER_DEFAULT } from "components/Collapse/constants"
import { renderComponent } from "components/Collapse/utils"

import { CollapseItemProps } from "./CollapseItemTypes"

export const CollapseItem = ({
  item,
  isExpanded,
  buildToggle,
  hasChevron,
  isEventPropagationStopped,
  chevronSize,
}: CollapseItemProps) => {
  const disabledPopoverContent =
    item.disabled && item.disabledPopoverMessage
      ? item.disabledPopoverMessage
      : null

  const chevronIcnName = isExpanded ? "icnChevronUp" : "icnChevronDown"

  const toggle = useMemo(() => {
    return buildToggle(item)
  }, [buildToggle, item])

  const header = useMemo(() => {
    return renderComponent({
      type: "header",
      component: item.header,
      isExpanded,
      toggle,
      isEventPropagationStopped,
    })
  }, [item.header, isExpanded, toggle, isEventPropagationStopped])

  const contents = useMemo(() => {
    return renderComponent({
      type: "contents",
      component: item.contents,
      isExpanded,
      toggle,
    })
  }, [item.contents, isExpanded, toggle])

  const shouldRenderContent: boolean = isExpanded || item.shouldAlwaysRenderContent

  return (
    <>
      <Popover
        content={disabledPopoverContent}
        placementsOrder={PLACEMENTS_ORDER_DEFAULT}
      >
        <div
          data-component-type="header"
          data-disabled={item.disabled}
          data-expanded={isExpanded}
          onClick={buildToggle(item)}
        >
          {hasChevron ? (
            <>
              <div data-chevron="true">
                <Icon
                  color="--color-icon-clickable"
                  name={chevronIcnName}
                  size={chevronSize}
                />
              </div>
              <div>{header}</div>
            </>
          ) : (
            header
          )}
        </div>
      </Popover>
      {shouldRenderContent ? (
        <div
          data-component-type="contents"
          data-has-chevron={hasChevron}
          data-is-rendered={!isExpanded && item.shouldAlwaysRenderContent}
        >
          {contents}
        </div>
      ) : null}
    </>
  )
}
