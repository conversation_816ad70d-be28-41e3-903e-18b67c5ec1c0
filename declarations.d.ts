declare module "*.svg" {
  import * as React from "react"

  export const ReactComponent: React.FunctionComponent<
    React.SVGProps<SVGSVGElement>
  >

  const src: string
  export default src
}

declare module "*.module.scss" {
  const classes: { [key: string]: string }
  export default classes
}

// declare module "styled-components" {
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   export interface CustomStyledComponentProps {
//     children?: ReactNode
//     className?: string
//     as?: keyof JSX.IntrinsicElements | React.ComponentType<any>
//   }
// }
