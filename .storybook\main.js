const path = require("path")

module.exports = {
  stories: ["../src/**/*.stories.@(js|jsx|ts|tsx)"],
  addons: ["@storybook/addon-links", "@storybook/addon-essentials"],
  core: {
    builder: "webpack5",
  },
  webpackFinal: async (config, { configType }) => {
    const fileLoaderRule = config.module.rules.find(
      (rule) => rule.test && rule.test.test(".svg")
    )
    fileLoaderRule.exclude = /\.svg$/

    // Используем Asset Modules для SVG в webpack 5
    config.module.rules.push({
      test: /\.svg$/,
      type: "asset/resource",
      use: ["@svgr/webpack"],
      generator: {
        filename: "static/icons/[name].[hash][ext]",
      },
    })

    // configType = DEVELOPMENT / PRODUCTION
    config.module.rules.push({
      test: /\.(sass|css|scss)$/,
      use: [
        "style-loader", // MiniCssExtractPlugin.loader for production mode
        "css-loader",
        "sass-loader",
      ],
      exclude: /node_modules/,
    })

    config.resolve.alias = {
      ...config.resolve.alias,
      utils: path.resolve(__dirname, "../src/utils/"),
      hooks: path.resolve(__dirname, "../src/hooks/"),
      hocs: path.resolve(__dirname, "../src/hocs/"),
      components: path.resolve(__dirname, "../src/components/"),
      assets: path.resolve(__dirname, "../src/assets/"),
      consts: path.resolve(__dirname, "../src/consts/"),
      globalStyles: path.resolve(__dirname, "../src/globalStyles/"),
      routes: path.resolve(__dirname, "../src/routes/"),
    }

    return config
  },
}
