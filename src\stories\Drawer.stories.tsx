import React, { useState } from "react"
import { ComponentMeta, ComponentStory } from "@storybook/react"

import { <PERSON><PERSON>, <PERSON>er as <PERSON>er<PERSON><PERSON>ponent, DrawerProps } from "components"

export default {
  title: "Drawer",
  component: DrawerComponent,
  parameters: {
    docs: {
      description: {
        component:
          "####Design - [Drawer](https://app.zeplin.io/project/6051cbfe3fed74441799d895/screen/64b65be06b38be2246da1698)",
      },
    },
  },
  argTypes: {
    isOpen: {
      description: "Is the drawer open",
      control: {
        type: "boolean",
      },
    },
    title: {
      description: "Title of the drawer",
    },
    subTitle: {
      description: "Sub title of the drawer",
    },
    side: {
      description: "Side of the drawer",
    },
    width: {
      description: "Width of the drawer",
      control: {
        type: "number",
      },
    },
    height: {
      description: "Height of the drawer",
      control: {
        type: "number",
      },
    },
    top: {
      description: "Top of the drawer",
      control: {
        type: "number",
      },
    },
    bottom: {
      description: "Bottom of the drawer",
      control: {
        type: "number",
      },
    },
    hasPadding: {
      description: "Controls if the drawer has padding",
    },
    hasBackdrop: {
      description: "Controls if the drawer has a backdrop",
    },
    hasBorderRadius: {
      description: "Controls if the drawer has a border radius",
    },
    onClose: {
      description: "Callback to close the drawer",
    },
    onBack: {
      description: "Callback for back icon onClick event",
    },
    onOk: {
      description: "Callback for OK button in footer onClick event",
    },
    hasHeader: {
      description: "Controls if the drawer has a header",
      control: {
        type: "boolean",
      },
    },
    hasFooter: {
      description: "Controls if the drawer has a footer",
      control: {
        type: "boolean",
      },
    },
    isStickyFooter: {
      description: "Controls if the drawer has a sticky footer",
      control: {
        type: "boolean",
      },
    },
    footer: {
      description:
        "Custom footer content. Accepts any elements or null if you want to hide footer",
    },
    footerClassName: {
      description: "Set className to modal footer",
    },
    isColumnButtonsOnMobile: {
      description:
        "Whether the buttons have full width and based one under another on mobile screens",
    },
    cancelButtonProps: {
      description: "The cancel button in footer props",
    },
    cancelButtonText: {
      description:
        "Text of the Cancel button in footer. If value doesn`t set button will be hidden",
    },
    okButtonText: {
      description:
        "Text of the Ok button in footer. If value doesn`t set button will be hidden",
    },
    okButtonProps: {
      description: "The ok button in footer props",
    },
    isClosable: {
      description:
        "Whether a Drawer can be closed. If You set true Close button will be hidden, closing by outside click will be unavailable",
    },
    isKeyboard: {
      description: "Whether support press esc to close",
    },
  },
} as ComponentMeta<typeof DrawerComponent>

const Template: ComponentStory<typeof DrawerComponent> = (
  props: DrawerProps
) => <DrawerComponent {...props} />

export const Drawer = Template.bind({})

export const DrawerDemo = () => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>Open Drawer</Button>
      <DrawerComponent
        isOpen={isOpen}
        subTitle="Subtitle"
        title="Title"
        onClose={() => setIsOpen(false)}
      >
        Contents
      </DrawerComponent>
    </>
  )
}
