import React from "react"
import { TableVirtuoso } from "react-virtuoso"

import { Header, <PERSON> } from "components/Table/components"
import { TableBaseData, TableComponentProps } from "components/Table/TableTypes"
import { checkIsArray } from "utils"

import {
  TableBodyComponent,
  TableComponent,
  TableFootComponent,
  TableRowComponent,
} from "./components"

import { FooterRow } from "../FooterRow"
import type { VirtualizedTableContext } from "./VirtualizedTableTypes"

const components = {
  TableRow: TableRowComponent,
  TableFoot: TableFootComponent,
  TableBody: TableBodyComponent,
  Table: TableComponent,
}

export const VirtualizedTable = <
  DataType extends TableBaseData,
  FilterValuesType
>({
  id,
  data,
  actionColumn,
  rowSpanMax,
  hasActiveFilters,
  headerColumns,
  bodyColumns,
  footerColumns,
  columnWidths,
  columnMinWidths,
  allIdsArray,
  isLoading,
  onEndReached,
  handleClear,
  renderCustomExpandedRow,
  noDataPlaceholder,
  hasStickyHeader,
  shouldDisplayDataTable,
  shouldDisplayNoDataPlaceholder,
  columnsCount,
  bordersVariant,
}: TableComponentProps<DataType, FilterValuesType>) => {
  const itemContent = (index, item) => {
    return (
      <Row<DataType, FilterValuesType>
        actionColumn={actionColumn}
        bordersVariant={bordersVariant}
        columns={bodyColumns}
        columnWidths={columnWidths}
        index={index}
        item={item}
        rowSpanMax={rowSpanMax}
      />
    )
  }

  const fixedHeaderContent = () => {
    return (
      <Header<DataType, FilterValuesType>
        actionColumn={actionColumn}
        columnMinWidths={columnMinWidths}
        columns={headerColumns}
        columnWidths={columnWidths}
        handleClear={handleClear}
        hasActiveFilters={hasActiveFilters}
        isLoading={isLoading}
      />
    )
  }

  const fixedFooterContent = () => {
    if (!checkIsArray(footerColumns)) {
      return null
    }

    return <FooterRow footerColumns={footerColumns} />
  }

  return (
    <TableVirtuoso<DataType, VirtualizedTableContext<DataType>>
      // @ts-ignore
      components={components}
      data={data}
      endReached={onEndReached}
      fixedFooterContent={fixedFooterContent}
      fixedHeaderContent={fixedHeaderContent}
      height="100%"
      increaseViewportBy={300}
      itemContent={itemContent}
      totalCount={data.length}
      context={{
        allIdsArray,
        id,
        renderCustomExpandedRow,
        noDataPlaceholder,
        hasStickyHeader,
        shouldDisplayDataTable,
        shouldDisplayNoDataPlaceholder,
        columnsCount,
      }}
    />
  )
}
