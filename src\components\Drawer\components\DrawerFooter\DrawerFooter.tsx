import React from "react"

import { Button } from "components/Button"

import { StyledButtonContainer, StyledDrawerFooter } from "./StyledDrawerFooter"

import { DrawerFooterProps } from "./DrawerFooterTypes"

export const DrawerFooter = ({
  footer,
  isColumnButtonsOnMobile,
  cancelButtonText,
  cancelButtonProps,
  okButtonText,
  okButtonProps,
  onClose,
  onOk,
  footerClassName,
  isLoading,
  isStickyFooter,
}: DrawerFooterProps) => {
  const isButtonTextDefined: boolean = !!(cancelButtonText || okButtonText)
  const shouldRenderFooter: boolean = !footer && isButtonTextDefined

  if (!shouldRenderFooter) {
    return null
  }

  return (
    <StyledDrawerFooter
      {...{ footer }}
      className={footerClassName}
      isStickyFooter={isStickyFooter}
    >
      {footer || (
        <StyledButtonContainer
          isColumnButtonsOnMobile={isColumnButtonsOnMobile}
        >
          {cancelButtonText ? (
            <Button
              disabled={isLoading || cancelButtonProps?.disabled}
              {...cancelButtonProps}
              fullWidth={isColumnButtonsOnMobile}
              variant="secondary"
              onClick={onClose}
            >
              {cancelButtonText}
            </Button>
          ) : null}
          {okButtonText ? (
            <Button
              disabled={isLoading || okButtonProps?.disabled}
              {...okButtonProps}
              fullWidth={isColumnButtonsOnMobile}
              variant="primary"
              onClick={onOk}
            >
              {okButtonText}
            </Button>
          ) : null}
        </StyledButtonContainer>
      )}
    </StyledDrawerFooter>
  )
}
