import type { ReactNode } from "react"

import { IconSizes } from "components/Icon/IconTypes"
import type { SizesTypes } from "types"

export type ItemKey = number | string

type CustomRenderParams = {
  isExpanded: boolean
  toggle: () => void
}

export type Item = {
  key: ItemKey
  header: ReactNode | ((params: CustomRenderParams) => ReactNode)
  contents: ReactNode | ((params: CustomRenderParams) => ReactNode)
  disabled?: boolean
  disabledPopoverMessage?: string
  shouldAlwaysRenderContent?: boolean
}

type OnChangeParams = {
  item: Item
  activeKeys: Array<ItemKey>
  activeKeysSet: Set<ItemKey>
}

export type Size = "small" | "medium" | "big"

export type CollapseProps = {
  items: Array<Item>
  activeKeys?: Array<ItemKey>
  defaultActiveKeys?: Array<ItemKey>
  variant?: "default" | "outlined"
  size?: Size
  headerPadding?: SizesTypes | "0" | string
  headerGap?: SizesTypes | "0"
  chevronSize?: IconSizes
  isContentAlignedWithArrowBox?: boolean
  isAccordion?: boolean
  isEventPropagationStopped?: boolean
  hasChevron?: boolean
  hasLeftAndRightBorders?: boolean
  hasTopAndBottomBorders?: boolean
  onChange?: (params: OnChangeParams) => void
}

export type StyledCollapseProps = Pick<
  CollapseProps,
  | "variant"
  | "isContentAlignedWithArrowBox"
  | "size"
  | "headerPadding"
  | "headerGap"
  | "chevronSize"
  | "hasLeftAndRightBorders"
  | "hasTopAndBottomBorders"
  | "isEventPropagationStopped"
  | "hasChevron"
>
