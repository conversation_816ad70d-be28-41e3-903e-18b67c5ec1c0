{"name": "@develop/fe-library", "version": "0.162.0-dev.1", "license": "UNLICENSED", "files": ["dist"], "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "./utils": {"types": "./dist/types/utils/index.d.ts", "import": "./dist/utils.js", "default": "./dist/utils.js"}, "./routes": {"types": "./dist/types/routes/index.d.ts", "import": "./dist/routes.js", "default": "./dist/routes.js"}, "./hocs": {"types": "./dist/types/hocs/index.d.ts", "import": "./dist/hocs.js", "default": "./dist/hocs.js"}, "./consts": {"types": "./dist/types/consts/index.d.ts", "import": "./dist/consts.js", "default": "./dist/consts.js"}, "./hooks": {"types": "./dist/types/hooks/index.d.ts", "import": "./dist/hooks.js", "default": "./dist/hooks.js"}, "./components": {"types": "./dist/types/components/index.d.ts", "import": "./dist/components.js", "default": "./dist/components.js"}, "./css": {"types": "./dist/root.css", "default": "./dist/root.css"}}, "typesVersions": {"*": {"index.d.ts": ["dist/types/index.d.ts"], "dist/hocs": ["dist/types/hocs/index.d.ts"], "dist/consts": ["dist/types/consts/index.d.ts"], "dist/hooks": ["dist/types/hooks/index.d.ts"], "dist/components": ["dist/types/components/index.d.ts"], "dist/utils": ["dist/types/utils/index.d.ts"], "dist/routes": ["dist/types/routes/index.d.ts"], "routes": ["dist/types/routes/index.d.ts"]}}, "main": "dist/index.js", "module": "dist/index.js", "types": "dist/types/index.d.ts", "scripts": {"start": "start-storybook -p 6006 -s public", "watch": "webpack serve --config webpack.config.dev.js", "clean": "shx rm -rf dist", "build-root-style": "sass src/assets/styles/root.scss  dist/root.css --style compressed", "build-lib:webpack": "yarn clean && webpack --config webpack.config.prod.js --mode production", "build-types": "tsc --project tsconfig.json", "build-lib": "yarn clean && yarn build-root-style && webpack --config webpack.config.prod.js --mode production && yarn build-types", "build-storybook": "build-storybook -s public", "test": "jest --passWithNoTests", "lint": "eslint src/**/*.{jsx,ts,tsx}", "lint:fix": "eslint src/**/*.{jsx,ts,tsx} --fix --color", "prettier": "prettier --write 'src/**/*.{jsx,ts,tsx}' --config ./.prettierrc", "prepack": "pinst --disable", "postpack": "pinst --enable", "prepare": "husky install", "commitlint": "commitlint -e -g './commitlint.config.js'"}, "dependencies": {"big.js": "^6.2.1", "classnames": "^2.3.2", "date-fns": "2.30.0", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "lodash.throttle": "^4.1.1", "lodash.uniqueid": "^4.0.1", "react": "^17.0.2", "react-color": "^2.19.3", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-hook-form": "^7.43.4", "react-intl-tel-input": "^8.2.0", "react-sortable-hoc": "^2.0.0", "react-virtuoso": "4.7.7", "styled-components": "5.3.6"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/preset-env": "^7.14.5", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@commitlint/cli": "^17.1.2", "@storybook/addon-actions": "^6.4.0", "@storybook/addon-essentials": "^6.4.0", "@storybook/addon-links": "^6.4.0", "@storybook/builder-webpack5": "^6.5.9", "@storybook/manager-webpack5": "^6.5.9", "@storybook/node-logger": "^6.4.0", "@storybook/preset-create-react-app": "^3.2.0", "@storybook/react": "^6.5.9", "@svgr/webpack": "^6.5.1", "@testing-library/react": "^10.0.2", "@types/big.js": "^6.1.6", "@types/lodash.debounce": "^4.0.9", "@types/lodash.get": "^4.4.9", "@types/lodash.isequal": "^4.5.8", "@types/lodash.throttle": "^4.1.9", "@types/lodash.uniqueid": "^4.0.9", "@types/react": "17.0.44", "@types/react-dom": "17.0.2", "@types/react-sizes": "^2.0.0", "@types/styled-components": "5.1.26", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "babel-plugin-module-resolver": "^4.1.0", "commitlint-config-jira": "^1.6.4", "commitlint-plugin-jira-rules": "^1.6.4", "css-loader": "^6.7.1", "eslint": "^8.23.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "latest", "eslint-plugin-jest": "^27.0.2", "eslint-plugin-jsx-a11y": "latest", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "latest", "eslint-plugin-simple-import-sort": "^8.0.0", "eslint-plugin-testing-library": "^5.6.3", "fork-ts-checker-webpack-plugin": "^7.2.13", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.0", "jest-image-snapshot": "^4.0.0", "jsdom-screenshot": "^3.2.0", "lint-staged": "^13.1.0", "pinst": "^3.0.0", "postcss": "^8.4.6", "postcss-loader": "^6.2.1", "postcss-scss": "^4.0.5", "prettier": "^2.7.1", "react-router-dom": "^6.3.0", "sass": "^1.64.1", "sass-loader": "^13.0.2", "shx": "^0.3.4", "style-loader": "^3.3.1", "stylelint": "^14.14.1", "stylelint-config-prettier": "^9.0.4", "stylelint-config-standard": "^29.0.0", "stylelint-config-styled-components": "^0.1.1", "stylelint-custom-processor-loader": "^0.6.0", "stylelint-processor-styled-components": "^1.10.0", "terser-webpack-plugin": "^5.3.3", "ts-loader": "^9.3.1", "typescript": "^4.7.4", "typescript-plugin-css-modules": "^3.4.0", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.3", "webpack-merge": "^5.8.0"}, "packageManager": "yarn@3.2.0", "resolutions": {"@types/react": "17.0.44", "@types/react-dom": "17.0.2", "styled-components": "^5"}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}