import styled, { css, FlattenSimpleInterpolation } from "styled-components"

import { getSize } from "utils"
import { gapSizes, paddingSizes } from "globalStyles"

import { calculatePaddingLeft } from "./utils"

import { StyledCollapseProps } from "./CollapseTypes"

const buildTopAndBottomBorders = ({
  hasTopAndBottomBorders,
}: StyledCollapseProps): FlattenSimpleInterpolation => {
  if (hasTopAndBottomBorders) {
    return css`
      border-top: var(--border-main);
      border-bottom: var(--border-main);
    `
  }
}

const buildLeftAndRightBorders = ({
  hasLeftAndRightBorders,
}: StyledCollapseProps): FlattenSimpleInterpolation => {
  if (hasLeftAndRightBorders) {
    return css`
      border-left: var(--border-main);
      border-right: var(--border-main);
    `
  }
}

const buildSizeStyles = ({
  headerPadding: headerPaddingProp,
  headerGap: headerGapProp,
}: StyledCollapseProps): FlattenSimpleInterpolation => {
  const headerPadding = getSize({
    offset: headerPaddingProp,
    sizes: paddingSizes,
  })

  const headerGap = getSize({ offset: headerGapProp, sizes: gapSizes })

  return css`
    padding: ${headerPadding};
    gap: ${headerGap};
  `
}

const buildPaddingStyles = (
  props: StyledCollapseProps
): FlattenSimpleInterpolation => {
  const paddingLeft = calculatePaddingLeft(props)

  if (!paddingLeft) {
    return
  }

  return css`
    &[data-has-chevron="true"] {
      padding-left: ${paddingLeft};
    }
  `
}

export const StyledCollapse = styled.div<StyledCollapseProps>`
  background: var(--color-main-background);
  width: min-content;
  min-width: 100%;
  ${buildTopAndBottomBorders};
  ${buildLeftAndRightBorders};

  > div {
    :not(:first-child) {
      border-block-end-width: 1px;
    }

    :not(:last-child) {
      margin-block-end: -1px;
      border-block-end-width: 1px;
    }

    &[data-component-type="header"] {
      box-sizing: border-box;
      align-self: stretch;
      flex-grow: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      background-color: var(--color-main-background);
      user-select: none;
      cursor: pointer;

      :not(:first-child) {
        border-top: var(--border-main);
      }

      :not(:last-child) {
        border-bottom: var(--border-main);
      }

      &[data-disabled="true"] {
        cursor: not-allowed;
      }

      > div {
        height: 100%;
        display: flex;
        align-items: center;
      }

      > div[data-chevron="true"] {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      > div:not([data-chevron="true"]) {
        flex-grow: 1;
        ${({ isEventPropagationStopped }) => {
          if (!isEventPropagationStopped) {
            return ""
          }

          return css`
            * {
              user-select: text;
              cursor: auto;
            }
          `
        }}
      }

      ${buildSizeStyles};
      ${({ variant }) => {
        const mapper = {
          default: css`
            background: var(--color-main-background);

            &[data-expanded="true"] {
              border-bottom: 0;

              & + div {
                border-top: 0;
              }
            }
          `,
          outlined: css`
            background: var(--color-background-second);
          `,
        }

        return mapper[variant]
      }};
    }

    &[data-component-type="contents"] {
      box-sizing: border-box;
      border-top: var(--border-main);

      :not(:last-child) {
        border-bottom: var(--border-main);
      }

      ${buildPaddingStyles};
    }

    &[data-is-rendered="true"] {
      display: none;
    }
  }
`
