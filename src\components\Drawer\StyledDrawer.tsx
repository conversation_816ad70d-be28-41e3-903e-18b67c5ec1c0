import styled, { css } from "styled-components"

import { DRAWER_SIZES } from "components/Drawer/constants"
import { convertToDimension } from "utils"
import { mainScreenSizes } from "globalStyles"

import { StyledDrawerProps } from "./DrawerTypes"

const { mediaMobileL } = mainScreenSizes

const defaultNumericParams = {
  width: "280px",
  height: "100%",
  top: "0",
}

const buildWidthStyles = ({ width }: StyledDrawerProps) => {
  const widthStyle =
    DRAWER_SIZES[width] || convertToDimension(width) || DRAWER_SIZES.xs

  return css`
    width: ${widthStyle};
    max-width: ${widthStyle};
  `
}

const buildGetNumericParam =
  (paramName: "height" | "top" | "bottom") =>
  ({ [paramName]: param }: StyledDrawerProps) => {
    if (!param) {
      return defaultNumericParams[paramName]
    }
    if (typeof param === "number") {
      return `${param}px`
    }

    return param
  }

const buildSideStyles = ({ side, isOpen }: StyledDrawerProps) => {
  if (side === "right") {
    const transform = isOpen ? "translateX(0)" : "translateX(100%)"

    return css`
      right: 0;
      transform: ${transform};
    `
  }

  const transform = isOpen ? "translateX(0)" : "translateX(-100%)"

  return css`
    left: 0;
    transform: ${transform};
  `
}

const buildPaddingStyles = ({ hasPadding }: StyledDrawerProps) => {
  if (hasPadding) {
    return css`
      padding-top: var(--padding-m);
      padding-bottom: var(--padding-m);
    `
  }
}

const buildBorderRadiusStyles = ({
  hasBorderRadius,
  hasPadding,
  side,
}: StyledDrawerProps) => {
  if (!hasBorderRadius || !hasPadding) {
    return
  }

  if (side === "right") {
    return css`
      border-top-left-radius: var(--border-radius);
      border-bottom-left-radius: var(--border-radius);
    `
  }

  return css`
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
  `
}

const buildTransitionStyles = ({ hasAnimation }: StyledDrawerProps) => {
  if (!hasAnimation) {
    return css`
      transition: none;
    `
  }

  return css`
    transition: transform 0.3s ease-in-out;
  `
}

export const StyledDrawer = styled.div<StyledDrawerProps>`
  position: fixed;
  ${buildWidthStyles};
  height: ${buildGetNumericParam("height")};
  max-height: ${buildGetNumericParam("height")};
  top: ${buildGetNumericParam("top")};
  bottom: ${buildGetNumericParam("bottom")};
  ${buildSideStyles};
  ${buildPaddingStyles};
  z-index: ${({ zIndex }) => zIndex};
  overflow-y: auto;
  ${buildTransitionStyles};

  @media (max-width: ${mediaMobileL}) {
    width: 100%;
    max-width: 100%;
  }

  > div {
    display: flex;
    flex-direction: column;
    background-color: var(--color-main-background);
    height: 100%;
    width: 100%;
    overflow: hidden;
    z-index: 1;
    position: relative;
    ${buildBorderRadiusStyles};

    div[data-component-type="contents"] {
      flex: 1;
      overflow: auto;
    }
  }
`
