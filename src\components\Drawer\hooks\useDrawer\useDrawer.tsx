import { useEffect } from "react"

import { useMediaBreakpoints } from "hooks"
import { breakpoints } from "consts"

import { useDrawerHandlers } from "../useDrawerHandlers"

import { UseDrawer } from "../../DrawerTypes"

export const useDrawer: UseDrawer = ({
  onBack,
  onClose,
  onOk,
  isOpen,
  isClosable,
  isClosedAfterOk,
  isKeyboard,
  isColumnButtonsOnMobile,
  hasHeader,
  title,
  subTitle,
}) => {
  const shouldRenderHeader: boolean =
    Boolean(title || subTitle || onBack || onClose) && hasHeader

  const { handleOk } = useDrawerHandlers({
    onClose,
    onOk,
    isOpen,
    isClosable,
    isClosedAfterOk,
    isKeyboard,
  })

  const isMobile = useMediaBreakpoints({
    end: breakpoints.mLG,
  })

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"

      return
    }

    document.body.style.overflow = "auto"

    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  return {
    shouldR<PERSON>Header,
    handleOk,
    shouldRenderColumnButtonsOnMobile: isMobile && isColumnButtonsOnMobile,
  }
}
