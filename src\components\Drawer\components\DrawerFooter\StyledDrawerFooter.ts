import styled from "styled-components"

import { mainScreenSizes } from "globalStyles"

import { DrawerFooterProps } from "./DrawerFooterTypes"

const { mediaMobileL } = mainScreenSizes

export const StyledDrawerFooter = styled.div<DrawerFooterProps>`
  border-top: var(--border-main);
  padding: var(--padding-m) var(--padding-l);
  display: flex;
  align-items: center;
  justify-content: ${({ footer }) => (footer ? null : "flex-end")};
  position: ${({ isStickyFooter }) => (isStickyFooter ? "sticky" : null)};
  bottom: ${({ isStickyFooter }) => (isStickyFooter ? 0 : null)};
  @media (max-width: ${mediaMobileL}) {
    padding: var(--padding-m);
  }
`

export const StyledButtonContainer = styled.div<DrawerFooterProps>`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  gap: var(--gap-m);

  @media (max-width: ${mediaMobileL}) {
    flex-direction: ${({ isColumnButtonsOnMobile }) =>
      isColumnButtonsOnMobile ? `column` : null};

    button {
      width: 100%;
    }
  }
`
