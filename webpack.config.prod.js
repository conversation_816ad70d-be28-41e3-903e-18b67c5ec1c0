const path = require("path")
const { merge } = require("webpack-merge")
const TerserPlugin = require("terser-webpack-plugin")
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin")

const baseWebpackConfig = require("./webpack.config.base")

module.exports = merge(baseWebpackConfig, {
  // Use separate entry points for each module to avoid code splitting issues
  entry: {
    index: path.resolve(__dirname, "src", "index.ts"),
    hooks: path.resolve(__dirname, "src", "hooks", "index.ts"),
    utils: path.resolve(__dirname, "src", "utils", "index.ts"),
    hocs: path.resolve(__dirname, "src", "hocs", "index.ts"),
    consts: path.resolve(__dirname, "src", "consts", "index.ts"),
    routes: path.resolve(__dirname, "src", "routes", "index.ts"),
    components: path.resolve(__dirname, "src", "components", "index.ts"),
  },
  devtool: "source-map",
  mode: "production",
  output: {
    filename: "[name].js",
    path: path.resolve(__dirname, "dist"),
    library: {
      type: "module",
    },
    module: true,
    publicPath: "",
    // Environment settings for compatibility
    environment: {
      arrowFunction: true,
      bigIntLiteral: false,
      const: true,
      destructuring: true,
      dynamicImport: true,
      forOf: true,
      module: true,
    },
  },
  experiments: {
    outputModule: true,
    // Disable top level await to avoid issues with code splitting
    topLevelAwait: false,
  },
  externals: {
    react: "react",
    "react-dom": "react-dom",
    "styled-components": "styled-components",
  },
  plugins: [
    // TypeScript type generation plugin - enhanced for production
    new ForkTsCheckerWebpackPlugin({
      typescript: {
        diagnosticOptions: {
          semantic: true,
          syntactic: true,
        },
        mode: "write-references",
        build: true,
        configFile: path.resolve(__dirname, "tsconfig.json"),
      },
      // Avoid issues with async checking
      async: false,
    }),
  ],
  optimization: {
    usedExports: true,
    providedExports: true,
    minimize: true,
    minimizer: [
      new TerserPlugin({
        parallel: 4,
        terserOptions: {
          compress: true,
          module: true, // Enable ES module optimizations
          mangle: {
            module: true, // Mangle ES module exports
          },
        },
      }),
    ],
    // Disable code splitting to avoid chunk loading issues
    splitChunks: false,
    runtimeChunk: false,
  },
})
