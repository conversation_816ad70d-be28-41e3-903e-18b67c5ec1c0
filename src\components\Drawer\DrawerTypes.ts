import { ReactNode } from "react"

import { ButtonProps } from "components/Button"

export type DrawerHeaderHeight = "s" | "m" | number | string

export type DrawerProps = {
  isOpen: boolean
  children: ReactNode
  title?: ReactNode
  subTitle?: string
  side?: "left" | "right"
  headerHeight?: "s" | "m" | number | string
  width?: "xs" | "s" | "m" | "l" | "xl" | number | string
  height?: number | string
  top?: number | string
  bottom?: number | string
  hasBackdrop?: boolean
  hasPadding?: boolean
  hasBorderRadius?: boolean
  hasHeaderBottomMargin?: boolean
  onClose?: () => void
  onBack?: () => void
  onOk?: () => void
  zIndex?: number
  hasAnimation?: boolean
  hasHeader?: boolean
  hasFooter?: boolean
  isStickyFooter?: boolean
  footer?: ReactNode
  footerClassName?: string
  isColumnButtonsOnMobile?: boolean
  isLoading?: boolean
  cancelButtonProps?: ButtonProps
  cancelButtonText?: string
  okButtonText?: string
  okButtonProps?: ButtonProps
  isClosedAfterOk?: boolean
  isClosable?: boolean
  isKeyboard?: boolean
}

export type StyledDrawerProps = Pick<
  DrawerProps,
  | "isOpen"
  | "side"
  | "width"
  | "height"
  | "top"
  | "bottom"
  | "hasPadding"
  | "hasBorderRadius"
  | "zIndex"
  | "hasAnimation"
>

type DrawerHandlers = {
  handleOk: () => void
}

export type UseDrawerHandlers = (
  params: Pick<
    DrawerProps,
    | "onClose"
    | "onOk"
    | "isKeyboard"
    | "isClosedAfterOk"
    | "isOpen"
    | "isClosable"
  >
) => DrawerHandlers

type UseDrawerReturn = DrawerHandlers & {
  shouldRenderHeader: boolean
  shouldRenderColumnButtonsOnMobile: boolean
}

export type UseDrawer = (
  params: Pick<
    DrawerProps,
    | "onClose"
    | "onBack"
    | "onOk"
    | "isKeyboard"
    | "isClosedAfterOk"
    | "isOpen"
    | "isClosable"
    | "isColumnButtonsOnMobile"
    | "hasHeader"
    | "title"
    | "subTitle"
  >
) => UseDrawerReturn
